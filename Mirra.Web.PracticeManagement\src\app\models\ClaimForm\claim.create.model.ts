import { HeaderProfessional837 } from "src/app/classmodels/ResponseModel/ClaimForm/ClaimPreviewModels/HeaderProfessional837";
import { InfoSourceProfessional837 } from "src/app/classmodels/ResponseModel/ClaimForm/ClaimPreviewModels/InfoSourceProfessional837";
import { OtherSubscriberInfoProfessional837 } from "src/app/classmodels/ResponseModel/ClaimForm/ClaimPreviewModels/OtherSubscriberInfoProfessional837";
import { ClaimStatusModel } from "./change.claim.status.model";

export class CreateClaimModel {
    fromDos: string;
    claimDosfrom: string;
    claimDosto: string;
    claimsProfessional837: ClaimsProfessional837Model;
    paidAmount: string;
    displayMemberFirstnameName: string;
    displayMemberLastName: string;
    facilityId: string;
    formCreatedDate: string;
    ipacode: string;
    insuranceCompany: string;
    lastModifiedBy: string;
    lastModifiedByFirstName: string;
    lastModifiedDate: string;
    userName: string;
    userLastName?: string;
    userFirstName?: string;
    userMiddleName?:string;
    lastModifiedByLastName?:string;
    lastModifiedByMiddleName?:string;
    reasons?:any
    claimFormStatusCode: string;
    uuid: string;
    claimType: string;
    totalCharges: string;
    patientCtrlNo: string;
    isActive: boolean;
    claimForm837Pid: number;
    claimsProfessional837id: number;
    filename: string;
    uploadExcelWarningMessage: string;
    status: string;
    source: string;
    notes: any[];
    icdversion?:string;
    icdcodes?:string;
    //View 
    subscribeId?: string;
    patientFirstName?: string;
    patientLastName?: string;
    patientMiddleName?: string;
    patientDob?: string;
    clm01PatientControlNo?: string;
    parentPatientCtrlNo?:string;
    clm02TotalClaimChargeAmount?: string;
    payerId?:string;
    fileType?:string;
    billingProviderId?:string;
    parentClaimForm837Pid?:number;
    onHoldCategory?:string
    _277pprocessedOn?:string;
    _277processedOnPayer?:string;
    _999processedOnPayer?:any;
    _999processedOnCh?: any;
    _277processedOnCh?: any;
    _835processedOnDate?: any;
    ignoreReason?: string;
    incomingFileId?: any;
    lstLinkedClaimDetails?: any;
    responseFileClaimMapings:any[];
    resubmissionTrackerId?:number;
    showNDC?:boolean;
    isSkippedDublicateClaim?:boolean =false;
    isSkippeMemberAddress:boolean =false;
    claimStatusModel:ClaimStatusModel;
}
export interface Claims837Model {
    fromDos: string;
    claimDosfrom: string;
    claimDosto: string;
    claimsProfessional837: ClaimsProfessional837Model;
}
export interface ClaimsProfessional837Model {

    subscriberkey: number;
    claimskey: number;
    dtp03HospitalizationDischargeDate: string;
    dtp03HospitalizationAdmissionDate: string;
    dtp03WorkReturnDate: string;
    dtp03LastWorkedDate: string;
    nm107ReferringProviderLastSuffix: string;
    nm105ReferringProviderLastMiddle: string;
    nm104ReferringProviderLastFirst: string;
    nm103ReferringProviderLastName: string;
    nm109ReferringProviderIdentifier: string;
    hcp14PolicyComplianceCode: string;
    dtp03AccidentDate?: string;
    dtp03OnsetofCurrentIllnessInjuryDate: string;
    clm1103RelatedCausesCode: string;
    clm1102RelatedCausesCode: string;
    clm1101RelatedCausesCode: string;
    clm1104AutoAccidentStateCode: string;
    clm0503ClaimFrequencyCode: string;
    clm06SupplierSignatureIndicator: string;
    clm08BenefitsAssignmentCertIndicator: string;
    nm109RenderingProviderIdentifier: string;
    nm104RenderingProviderFirst: string;
    nm105RenderingProviderMiddle: string;
    nm103RenderingProviderLastOrOrganizationName: string;
    nm109LabFacilityIdentifier: string;
    nm103LabFacilityName: string;
    n403LabFacilityZip: string;
    n402LabFacilityState: string;
    n401LabFacilityCity: string;
    n302LabFacilityAddress2: string;
    n301LabFacilityAddress1: string;
    clm0501PlaceOfServiceCode: string;
    prv03ProviderTaxonomyCode: string;
    dtp03LastSeenDate: string;
    dtp03InitialTreatmentDate: string;
    nm104SupervisingPhysicianFirst: string;
    nm103SupervisingPhysicianLastName: string;
    nm105SupervisingPhysicianMiddle: string;
    nm109SupervisingPhysicianIdentifier: string;

    n301AmbulancePickupAddress1: string;
    n302AmbulancePickupAddress2: string;
    n401AmbulancePickupCity: string;
    n402AmbulancePickupState: string;
    n403AmbulancePickupZip: string;
    n404AmbulancePickupCountryCode: string;
    n407AmbulancePickupCountrySubdivisionCode: string;
    n103AmbulanceDropOffLocation: string;
    n301AmbulanceDropOffAddress1: string;
    n302AmbulanceDropOffAddress2: string;
    n401AmbulanceDropOffCity: string;
    n402AmbulanceDropOffState: string;
    n403AmbulanceDropOffZip: string;
    n404AmbulanceDropOffCountryCode: string;
    n407AmbulanceDropOffCountrySubdivisionCode: string;
    ref02PriorAuthorizationNumber?: string;
    ref02PayerClaimControlNumber?: string;
    amt02PatientAmountPaid?: string;
    hi0102DiagnosisCode?: string;
    hi0202DiagnosisCode2?: string;
    clm01PatientControlNo?:string;
    hi0302DiagnosisCode3?: string;
    hi0402DiagnosisCode4?: string;
    hi0502DiagnosisCode5?: string;
    hi0602DiagnosisCode6?: string;
    hi0702DiagnosisCode7?: string;
    hi0802DiagnosisCode8?: string;
    hi0902DiagnosisCode9?: string;
    hi1002DiagnosisCode10?: string;
    hi1102DiagnosisCode11?: string;
    hi1202DiagnosisCode12?: string;
    clm07PlanParticipationCode?:string;
    ref02MammographyCertificationNumber?:string;
    otherInfoProfessionalOtherInfoProfessional: OtherInfoProfessionalOtherInfoProfessionalModel;
    subscriberkeyNavigation: SubscriberkeyNavigationModel;
    serviceLineProfessional837s: ServiceLineProfessional837sModel[];
    otherSubscriberInfoProfessional837s?: OtherSubscriberInfoProfessional837[];
    claimListDetails: ClaimListDetailsModel[];
    nmSupervisingProviderId2?:string;
    per04SubscriberPhoneNo?:string;
}
export interface ClaimListDetailsModel {
    iCDCodes: ICDCodesModel[];
    serviceLineDetails: ServiceLineDetailsModel[]
}
export interface ICDCodesModel {
    iCD: string;
}
export interface ServiceLineDetailsModel {
    DOSFrom: string;
    DOSTo: string;
    dOSFrom: string;
    dOSTo: string;
    claimskey: number;
    n301ServiceFacilityAddress1: string;
    n302ServiceFacilityAddress2: string;
    n401ServiceFacilityCity: string;
    n402ServiceFacilityState: string;
    n403ServiceFacilityZip: string;
    nm103RenderingProviderNameLastOrg: string;
    nm109RenderingProviderId: string;
    nm109ServiceFacilityId: string;
    sv10102ProcedureCode: string;
    sv10103ProcedureModifier1: string;
    sv10104ProcedureModifier2: string;
    sv10105ProcedureModifier3: string;
    sv10106ProcedureModifier4: string;
    sv102LineItemChargeAmount: string;
    sv104ServiceUnitCount: string;
    sv105PlaceOfServiceCode: string;
    sv10701DiagnosisCodePointer1: string;
    sv10702DiagnosisCodePointer2: string;
    sv10703DiagnosisCodePointer3: string;
    sv10704DiagnosisCodePointer4: string;
    sv109EmergencyIndicator: string;
    sv111EpsdtIndicator: string;
    sv10101ProductServiceIdQualifier:string;
    nte02LineNoteText?:string;
    anesStart?:string;
    anesStop?:string;
    lin03NationalDrugCode?:string;
    ctp04NationalDrugUnitCount?:string;
    lin02NationalDrugCodeQlfr?:string;
    ctp0501UnitMeasurementCode?:string;
    cpt03NationalDrugUnitPrice?:string; 
    lx01AssignedNumber?:string;
    sv10107ServiceDescription?:string;
    serviceLinekey: number; 
    nmSupervisingProviderId2?:string;
    dtp03ServiceDate?:string;
    svServiceFacilityId2?:string; 
    nm103SupervisingProviderLastName?:  string;
    nm104SupervisingProviderFirst?:  string;
    nm105SupervisingProviderMiddle?:  string;
    nm107SupervisingProviderSuffix?:  string;
    nm109SupervisingProviderId?: string;
}
export interface ServiceLineProfessional837sModel {
    DOSFrom: string;
    DOSTo: string;
    dOSFrom: string;
    dOSTo: string;
    claimskey: number;
    n301ServiceFacilityAddress1: string;
    n302ServiceFacilityAddress2: string;
    n401ServiceFacilityCity: string;
    n402ServiceFacilityState: string;
    n403ServiceFacilityZip: string;
    nm103RenderingProviderNameLastOrg: string;
    nm109RenderingProviderId: string;
    nm109ServiceFacilityId: string;
    sv10102ProcedureCode: string;
    sv10103ProcedureModifier1: string;
    sv10104ProcedureModifier2: string;
    sv10105ProcedureModifier3: string;
    sv10106ProcedureModifier4: string;
    sv102LineItemChargeAmount: string;
    sv104ServiceUnitCount: string;
    sv105PlaceOfServiceCode: string;
    sv10701DiagnosisCodePointer1: string;
    sv10702DiagnosisCodePointer2: string;
    sv10703DiagnosisCodePointer3: string;
    sv10704DiagnosisCodePointer4: string;
    sv109EmergencyIndicator: string;
    sv111EpsdtIndicator: string;
    svServiceFacilityId2?: String;
    nm103OrderingProviderLastName?: string;
    nm104OrderingProviderFirst?: string;
    nm105OrderingProviderMiddle?: string;
    nm109OrderingProviderId?: string;
    nmOrderingProviderId2?: string;
    ref02ReferringCliaNumber?: string;
    dtp03ServiceDate?: string;
    sv10107ServiceDescription?:string;
    ref02MammographyCertificationNumber?:string;
    serviceLinekey:number;
    nmSupervisingProviderId2?:string;
    anesStart?:string;
    anesStop?:string;
    lin03NationalDrugCode?:string;
    ctp04NationalDrugUnitCount?:string;
    lin02NationalDrugCodeQlfr?:string;
    ctp0501UnitMeasurementCode?:string;
    cpt03NationalDrugUnitPrice?:string; 
    lx01AssignedNumber?:string; 
    sv10101ProductServiceIdQualifier?:string;
    nte02LineNoteText?:string;  
    nm103SupervisingProviderLastName?:  string;
    nm104SupervisingProviderFirst?:  string;
    nm105SupervisingProviderMiddle?:  string;
    nm107SupervisingProviderSuffix?:  string;
    nm109SupervisingProviderId?: string;
}
export interface SubscriberkeyNavigationModel {
    dmg02SubscriberBirthDate: string
    dmg03SubscriberGenderCode: string;
    n301PayerAddr1: string;
    n301SubscriberAddr1: string;
    n302PayerAddr2: string;
    n302SubscriberAddr2: string;
    n401PayerCity: string;
    n401SubscriberCity: string;
    n402PayerState: string;
    n402SubscriberState: string;
    n403PayerZip: string;
    n403SubscriberZip: string;
    nm103PayerLastOrOrganizatioName: string;
    nm103SubscriberLastOrOrganizationName: string;
    nm104SubscriberFirst: string;
    nm105SubscriberMiddle: string;
    nm109PayerIdCode: string;
    nm109SubscriberIdCode: string;
    per04SubscriberPhoneNo: string;
    sbr02IndividualRelationshipCode: string;
    sbr04SubscriberGroupName: string;
    sbr09ClaimFilingIndicatorCode: string;
    subscriberkey: number;
    sbr03SubscriberGroupPolicyNo?: string;
    ref02PropertyCasualtyClaimNo?: string;
    dependentProfessional837s: DependentProfessional837sModel[];
    infoSourcekeyNavigation: InfoSourceProfessional837;
    crcConditionsIndicatorProfessional837s: CRCConditionasIndicatoeProfessional837s[];
}

export interface DependentProfessional837sModel {
    dmg02PatientBirthDate: string;
    dmg03PatientGenderCode: string;
    n301PatientAddr1: string;
    n302PatientAddr2: string;
    n401PatientCity: string;
    n402PatientState: string;
    n403PatientZip: string;
    nm103PatientLastOrOrganizationName: string;
    nm104PatientFirst: string;
    nm105PatientMiddle: string;
    pat01IndividualRelationshipCode: string;
    per04PatientPhoneNo: string;
    dependentkey?:number;
    subscriberkey?:number;
    subscriberProfessional837id?:string;
}
export interface InfoSourcekeyNavigationModel {
    infosourcekey: number;
    n302BillingProviderAddr2: string;
    n401BillingProviderCity: string;
    n402BillingProviderState: string;
    n403BillingProviderZip: string;
    nm103BillingProviderLastOrOrganizationName: string;
    nm104BillingProviderFirst: string;
    nm105BillingProviderMiddle: string;
    nm109BillingProviderIdCode: string;
    prv03BillingProviderIdCode: string;
    ref02BillingProviderEmployerId: string;
    perAdministrativeCommunicationContactProfessional837s: PerAdministrativeCommunicationContactProfessional837sModel[];
}

export interface PerAdministrativeCommunicationContactProfessional837sModel {
    per02ContactName: string;
    per0xEmail: string;
    per0xFaxNo: string;
    per0xPhoneNo: string;
    per0xPhoneExtNo: string;
    perkey?:number;
    // headerkeyNavigation:HeaderProfessional837;
}
export interface OtherInfoProfessionalOtherInfoProfessionalModel {
    otherInfoProfessionalId?:number;
    billingGroupIdQlfr: string;
    billingGroupNumber: string;
    dateOfCurrentIllnessQlfr: string;
    icdidentifier: string;
    insurancePlanName: string;
    insuredOtherHealthBenefitPlan: string;
    insuredSignature: string;
    outsideLab: string;
    patientConditionRelatedToAutoAccident: string;
    patientConditionRelatedToAutoAccidentState: string;
    patientConditionRelatedToEmp: string;
    patientConditionRelatedToOtherAccident: string;
    patientOrAuthorizedSignature: string;
    patientOrAuthorizedSignatureDate: string;
    patientOtherDate: string;
    renderingProviderPin: string;
    patientOtherDateQlfr: string;
    reservedNuccuse0801: string;
    reservedNuccuse0802: string;
    otherInsuredNuccuse01: string;
    otherInsuredNuccuse02: string;
    otherClaimIdNuccuse: string;
    additionalClaimInfoNucc: string;
    patientConditionNuccuse: string;
    claimsCodes?: string;
    federalTaxIdType?: string;
    physicianQlfr?: string;
    physicianDescription?: string;
    resubmissionCode?: string;
    reservedNuccuse30?:string;
}
export interface CRCConditionasIndicatoeProfessional837s {
    claimskey?: number;
    dependentProfessional837s: DependentProfessional837sModel;
    dtP_DateTimePeriod_Professional_837?: DTPDateTimePeriodProfessional837[];
}

export interface DTPDateTimePeriodProfessional837 {
    dependentkey?: number;
    serviceLinekeyNavigation: ServiceLinekeyNavigation;
}

export interface ServiceLinekeyNavigation {
    nm104SupervisingProviderFirst?: string;
    nm103SupervisingProviderLastName?: string;
    nm105SupervisingProviderMiddle?: string;
    nm108RenderingProviderIdqlfr?: string;
    sv10101ProductServiceIdQualifier?:string;
    ref02MammographyCertificationNumber?:string;
}


