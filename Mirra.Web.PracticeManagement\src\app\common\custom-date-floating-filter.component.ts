import { Compo<PERSON>, OnIni<PERSON>, <PERSON><PERSON>hild, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { IFloatingFilter, IFloatingFilterParams } from 'ag-grid-community';
import { AgFrameworkComponent } from 'ag-grid-angular';
import { Subject, debounceTime, distinctUntilChanged, takeUntil } from 'rxjs';

@Component({
  selector: 'app-custom-date-floating-filter',
  template: `
    <input #input 
           type="text" 
           (input)="onInput($event)" 
           (blur)="onBlur()"
           [value]="currentValue" 
           placeholder="mm/dd/yyyy"
           class="ag-floating-filter-input"
           [class.error]="hasError"
           maxlength="10"/>
    <div *ngIf="errorMsg" class="error-message">{{ errorMsg }}</div>
  `,
  styles: [`
    .ag-floating-filter-input {
      width: 100%;
      height: 100%;
      border: none;
      outline: none;
      padding: 4px 8px;
      font-size: 12px;
      box-sizing: border-box;
    }
    .ag-floating-filter-input.error {
      border: 1px solid #ff4444 !important;
      background-color: #fff5f5;
    }
    .error-message {
      color: #ff4444;
      font-size: 10px;
      position: absolute;
      top: 100%;
      left: 0;
      white-space: nowrap;
      z-index: 1000;
      background: white;
      padding: 2px 4px;
      border: 1px solid #ff4444;
      border-radius: 2px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
  `]
})
export class CustomDateFloatingFilterComponent implements IFloatingFilter, OnInit, OnDestroy, AgFrameworkComponent<IFloatingFilterParams> {
  @ViewChild('input', { static: true }) input: ElementRef<HTMLInputElement>;
  
  params: IFloatingFilterParams;
  currentValue: string = '';
  errorMsg: string = '';
  hasError: boolean = false;
  
  private destroy$ = new Subject<void>();
  private inputSubject = new Subject<string>();

  agInit(params: IFloatingFilterParams): void {
    this.params = params;
    
    // Set up debounced input handling
    this.inputSubject.pipe(
      debounceTime(300), // 300ms delay after user stops typing
      distinctUntilChanged(),
      takeUntil(this.destroy$)
    ).subscribe(value => {
      this.processFilterValue(value);
    });
  }

  ngOnInit() { }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onInput(event: any) {
    let value = event.target.value;
    
    // Auto-format as user types
    value = this.formatDateInput(value);
    
    // Update the input value with formatting
    if (value !== event.target.value) {
      event.target.value = value;
    }
    
    this.currentValue = value;
    this.clearError();
    
    // If empty, clear filter immediately
    if (!value || value.trim() === '') {
      this.clearFilter();
      return;
    }
    
    // Add to debounced input stream
    this.inputSubject.next(value);
  }

  onBlur() {
    // Final validation on blur
    if (this.currentValue && !this.isValidDate(this.currentValue)) {
      this.setError('Please enter a valid date (mm/dd/yyyy)');
    }
  }

  private formatDateInput(input: string): string {
    // Remove all non-numeric characters except existing slashes
    let cleaned = input.replace(/[^\d\/]/g, '');
    
    // Remove extra slashes
    let parts = cleaned.split('/');
    if (parts.length > 3) {
      parts = parts.slice(0, 3);
    }
    
    // Auto-add slashes
    if (cleaned.length >= 2 && cleaned.indexOf('/') === -1) {
      cleaned = cleaned.substring(0, 2) + '/' + cleaned.substring(2);
    }
    if (cleaned.length >= 5 && cleaned.split('/').length === 2) {
      let parts = cleaned.split('/');
      cleaned = parts[0] + '/' + parts[1].substring(0, 2) + '/' + parts[1].substring(2);
    }
    
    // Limit length
    if (cleaned.length > 10) {
      cleaned = cleaned.substring(0, 10);
    }
    
    return cleaned;
  }

  private processFilterValue(value: string) {
    if (!value || value.trim() === '') {
      this.clearFilter();
      return;
    }

    if (!this.isValidDate(value)) {
      this.setError('Please enter a valid date (mm/dd/yyyy)');
      return;
    }

    // If we get here, the date is valid - apply the filter
    this.clearError();
    this.applyFilter(value);
  }

  private isValidDate(dateString: string): boolean {
    // Check format first (mm/dd/yyyy)
    const dateRegex = /^(0[1-9]|1[0-2])\/(0[1-9]|[12]\d|3[01])\/\d{4}$/;
    if (!dateRegex.test(dateString)) {
      return false;
    }

    // Parse and validate the actual date
    const [month, day, year] = dateString.split('/').map(Number);
    const date = new Date(year, month - 1, day);
    
    // Check if the date is valid (handles leap years, month lengths, etc.)
    if (date.getFullYear() !== year || 
        date.getMonth() !== month - 1 || 
        date.getDate() !== day) {
      return false;
    }

    // Optional: Add business rule validation (date range)
    const currentDate = new Date();
    const minDate = new Date(currentDate.getFullYear() - 10, 0, 1); // 10 years ago
    const maxDate = new Date(currentDate.getFullYear() + 2, 11, 31); // 2 years ahead
    
    if (date < minDate || date > maxDate) {
      return false;
    }

    return true;
  }

  private setError(message: string) {
    this.errorMsg = message;
    this.hasError = true;
  }

  private clearError() {
    this.errorMsg = '';
    this.hasError = false;
  }

  private clearFilter() {
    this.clearError();
    this.params.parentFilterInstance(instance => {
      instance.onFloatingFilterChanged('equals', null);
    });
  }

  private applyFilter(value: string) {
    this.params.parentFilterInstance(instance => {
      instance.onFloatingFilterChanged('equals', value);
    });
  }

  onParentModelChanged(parentModel: any): void {
    // Update the input value when the parent filter changes
    if (parentModel && parentModel.filter) {
      this.currentValue = parentModel.filter;
    } else {
      this.currentValue = '';
    }
    this.clearError();
  }
}
