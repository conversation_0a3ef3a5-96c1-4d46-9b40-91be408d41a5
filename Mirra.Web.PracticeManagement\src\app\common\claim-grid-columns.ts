
import { CellClickedEvent, ColDef, ValueFormatterParams } from "ag-grid-community"
import { DateFormatter, DateFormatterWithTime, SortAgeValues, getInDays } from 'src/app/shared/functions/dateFormatterFunction';
import { DOSSortingDashboard, SortDateValues } from 'src/app/shared/functions/sort-for-dates';
import { OpenClaimActionRender } from "../shared/Renderer/claim-grid/OpenClaimActionRenderer";
import { DispatchedClaimActionRender } from "../shared/Renderer/claim-grid/DispatchedClaimActionRenderer";
import { UnAckByCHClaimActionRenderer } from "../shared/Renderer/claim-grid/UnAckByCHClaimActionRenderer";
import { RejectedByCHActionRenderer } from "../shared/Renderer/claim-grid/RejectedByCHActionRenderer";
import { EobRecievedClaimActionRenderer } from "../shared/Renderer/claim-grid/EOBRecievedClaimActionRenderer";
import ChildClaimIndicatorRenderer from "../shared/Renderer/claim-grid/ChildClaimIndicatorRenderer";
import { AcceptedClaimActionRenderer } from "../shared/Renderer/claim-grid/AcceptedClaimActionRenderer";
import { AcceptedClaimLockerActionRenderer } from "../shared/Renderer/claim-grid/AcceptedClaimLockerActionRenderer";
import { CLAIM_TYPE, COMMON_METHODS, COMMON_VALUES } from "./common-static";
import InActiveClaimColorIndicatorRenderer from "../shared/Renderer/claim-grid/InActiveClaimColorIndicatorRenderer";
import ChildClaimDeActiveClaimIndicatorRenderer from "../shared/Renderer/claim-grid/ClaimColorIndicatorRenderer";
import { OnHoldReasonActionRenderer } from "../shared/Renderer/claim-grid/OnHoldReasonActionRenderer";
import InActiveClaimStatusColorIndicatorRenderer from "../shared/Renderer/claim-grid/InActiveClaimStatusColorIndicatorRenderer";
import { EobReportsReasonRenderer } from "../shared/Renderer/claim-grid/EobReportsReasonRenderer";
import { EobReportAnchorRenderer } from "../shared/Renderer/claim-grid/EobReportAnchorRenderer";
import { UnAckByPayerActionRenderer } from "../shared/Renderer/claim-grid/UnAckByPayerActionRenderer";
import { Customize } from "../models/customize";
import { DispatchedResubmsnExclaimarkRenderer } from "../shared/Renderer/claim-grid/DispatchedResubmsnExclaimarkRenderer";
import { ResubmissionExclaimarkRenderer } from "../shared/Renderer/claim-grid/ResubmissionExclaimarkRenderer";
import { PendingResubmissionExclaimarkRenderer } from "../shared/Renderer/claim-grid/PendingResubmissionExclaimarkRenderer";


export class CLAIM_GRID_DEFCOLUMNS {
 
  static OPEN_COLUMN_DEFS(privMoveAllOpenToAccepted: boolean, isPrivMoveSelectedOpenToAccepted: boolean, billerPrivMoveAllOpenToAccepted: boolean) {
    const OPEN_COLUMN_DEFS: ColDef[] = [
       { headerName: ' ', width: 5, lockPosition: 'left', filter: false, resizable: false, field: '', pinned: 'left', cellRenderer: ChildClaimDeActiveClaimIndicatorRenderer,
       },
       {
        headerName: '', minWidth: 5, lockPosition: 'left', filter: false, resizable: false, field: '', pinned: 'left',
        checkboxSelection: params => (params?.data?.claimId > 0 && ShowHideCheckboxClaimsGrid.showHideCheckbox(params) && (privMoveAllOpenToAccepted || isPrivMoveSelectedOpenToAccepted || billerPrivMoveAllOpenToAccepted)) ? true : false,
        headerCheckboxSelection: privMoveAllOpenToAccepted,
        sortable: false,
        headerComponent: 'selectAllComponent',
       },
       { headerName: 'S.No', minWidth: 40, lockPosition: 'left', filter: false, resizable: false, field: '', pinned: 'left', valueGetter:  params => {
         if (params?.node && params.node.id != null) {
           // For infinite row model, use the node id which represents the actual row number
           return parseInt(params.node.id) + 1;
         }
         return '';
       },cellClass: 'ag-grid-row-number-cell'
       },
      
      { headerName: 'Claim ID', minWidth: 160, resizable: false, pinned: 'left', field: 'claimControlNumber', sortable: true, tooltipField: 'claimControlNumber' },

      { headerName: 'Payer', minWidth: 200, resizable: true, field: 'payer', tooltipField: 'payer',sortable: true, valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params) },
      { headerName: 'Member', minWidth: 200, resizable: true, field: 'memberFullName', tooltipField: "memberFullName", sortable: true, valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params) },
      {
        headerName: 'DOS', minWidth: 200, resizable: true, field: 'dos', tooltipField: "dos", sortable: true,
        filter: 'agDateColumnFilter',
        floatingFilterComponent: 'customDateFloatingFilter',
        floatingFilterComponentParams: {
          suppressFilterButton: true,
          debounceMs: 300
        },
        filterValueGetter: params => {
          return DateFormatter(params.data.dos)
        },
        comparator(valueA, valueB, nodeA, nodeB) {
          return DOSSortingDashboard(nodeA.data.dosFrom, nodeB.data.dosFrom, nodeA.data.dosTo, nodeB.data.dosTo)
        }
      },
      { headerName: 'Rendering Provider', minWidth: 210, resizable: true, field: 'renderingProviderFullName', tooltipField: "renderingProviderFullName", sortable: true , valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params)},
      {
        headerName: 'Claimed Amount', minWidth: 160, resizable: false, cellClass: 'align-right', field: 'claimAmount', tooltipField: "claimAmount", sortable: true
        , valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.claimAmount),
      },

      { headerName: 'Claim Type', minWidth: 150, resizable: true, field: 'claimType', tooltipField: "claimType", sortable: true },
      {
        headerName: 'Created On', minWidth: 150, filter: 'agDateColumnFilter',  resizable: true, field: 'dateCreated',   sortable: true ,   cellRenderer: params => {
          return DateFormatter(params.value)
        }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
          return SortDateValues(valueA, valueB)
        }, filterValueGetter: params => {
          return DateFormatter(params?.data?.dateCreated)
        }
      },
      {
        headerName: 'Age(DOC)', minWidth: 150, resizable: false, field: 'age', sortable: true
      },
      { headerName: 'Created By', minWidth: 200, resizable: false, field: 'createdBy', filter: true, tooltipField: "createdBy",sortable: true },
      { headerName: 'Action', minWidth: 115, resizable: false, lockVisible: true, cellRenderer: OpenClaimActionRender, sortable: false, type: 'centerAligned', filter: false, hide: false, pinned: 'right' },
    ];

    return OPEN_COLUMN_DEFS;
  }

  static ONHOLD_COLUMN_DEFS: ColDef[] = [
    { headerName: '', minWidth: 10, lockPosition: 'left', filter: false, resizable: false, field: '', pinned: 'left', cellRenderer: ChildClaimDeActiveClaimIndicatorRenderer },
    { headerName: 'S.No', minWidth: 15, lockPosition: 'left', filter: false, resizable: false, field: '', pinned: 'left',valueGetter:  params => { return   params?.node?.id != null ? parseInt(params.node.id) + 1 : ''   }, cellClass: 'ag-grid-row-number-cell' },
    { headerName: 'Claim ID', minWidth: 160, resizable: false, pinned: 'left', field: 'claimControlNumber',sortable: true, tooltipField: 'claimControlNumber' },
    { headerName: 'Payer', minWidth: 250, resizable: false, field: 'payer', tooltipField: 'payer',sortable: true, valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params) },
    { headerName: 'Member', minWidth: 200, resizable: true, field: 'memberFullName', tooltipField: "memberFullName", sortable: true, valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params) },
    { headerName: 'Rendering Provider', minWidth: 200, resizable: true, field: 'renderingProviderFullName', tooltipField: "renderingProviderFullName", sortable: true, valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params) },
    {
      headerName: 'DOS', minWidth: 200, filter: 'agDateColumnFilter' ,resizable: false,sortable: true, field: 'dos', tooltipField: "dos", comparator(valueA, valueB, nodeA, nodeB) {
        return DOSSortingDashboard(nodeA.data.dosFrom, nodeB.data.dosFrom, nodeA.data.dosTo, nodeB.data.dosTo)},
         filterValueGetter: params => {
          return DateFormatter(params.data.dos)
        }
    },
    {
      headerName: 'Claimed Amount', minWidth: 155, resizable: false, cellClass: 'align-right',sortable: true,  field: 'claimAmount', tooltipField: "claimAmount"
      , valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.claimAmount),
    },
    {
      headerName: 'Created On', minWidth: 150, resizable: false, filter: 'agDateColumnFilter', sortable: true, field: 'dateCreated', cellRenderer: params => {
        return DateFormatter(params.value)
      }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
        return SortDateValues(valueA, valueB)
      }, filterValueGetter: params => {
        return DateFormatter(params?.data?.dateCreated)
      }
    },
    { headerName: 'Created By', minWidth: 200, resizable: false, field: 'createdBy',sortable: true,  filter: true, tooltipField: "createdBy" },
    { headerName: 'Reviewed By', minWidth: 200, resizable: false, field: 'reviewedBy',sortable: true,  filter: true, tooltipField: "reviewedBy" },
    {
      headerName: 'OnHold on', minWidth: 180, resizable: false, filter: 'agDateColumnFilter',sortable: true, field: 'statusModifiedDate', cellRenderer: params => {
        return DateFormatter(params.value)
      }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
        return SortDateValues(valueA, valueB)
      }, filterValueGetter: params => {
        return DateFormatter(params?.data?.statusModifiedDate)
      }
    },
    { headerName: 'OnHold Reason', minWidth: 180, resizable: true, lockVisible: true, field: 'statusReason', cellRenderer: OnHoldReasonActionRenderer, sortable: true, type: 'centerAligned', filter: true, hide: false, pinned: 'right',valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params) },

    { headerName: 'Action', minWidth: 115, resizable: false, lockVisible: true, cellRenderer: OpenClaimActionRender, sortable: false, type: 'centerAligned', filter: false, hide: false, pinned: 'right' }
  ];

  static RESUBMISSION_COLUMN_DEFS: ColDef[] = [

    { headerName: '', minWidth: 5, filter: false, lockPosition: 'left', resizable: false, field: '', pinned: 'left', cellRenderer: InActiveClaimColorIndicatorRenderer },
    { headerName: 'S.No', minWidth: 15, lockPosition: 'left', filter: false, resizable: false, field: '', pinned: 'left', valueGetter:  params => { return   params?.node?.id != null ? parseInt(params.node.id) + 1 : ''   },cellClass: 'ag-grid-row-number-cell' },

    { headerName: 'Claim ID', minWidth: 160, resizable: false, sortable: true, pinned: 'left', field: 'claimControlNumber', tooltipField: 'claimControlNumber' },
    { headerName: 'Payer', minWidth: 200, resizable: false, sortable: true,  field: 'payer', tooltipField: 'payer', valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params)},
    { headerName: 'Member', minWidth: 200, resizable: true,sortable: true, field: 'memberFullName', tooltipField: "memberFullName", valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params) },
    { headerName: 'Rendering Provider', minWidth: 200, resizable: true, sortable: true, field: 'renderingProviderFullName', tooltipField: "renderingProviderFullName", valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params) },
    {
      headerName: 'DOS', minWidth: 200, resizable: true, field: 'dos',sortable: true, filter: 'agDateColumnFilter', tooltipField: "dos", comparator(valueA, valueB, nodeA, nodeB) {
        return DOSSortingDashboard(nodeA.data.dosFrom, nodeB.data.dosFrom, nodeA.data.dosTo, nodeB.data.dosTo)
      },
         filterValueGetter: params => {
          return DateFormatter(params.data.dos)
        }
    },
    { headerName: 'Claim Type', minWidth: 150, resizable: false,sortable: true, field: 'claimType', tooltipField: "claimType" },
    {
      headerName: 'Claimed Amount', minWidth: 155, resizable: false,sortable: true, cellClass: 'align-right', field: 'claimAmount', tooltipField: "claimAmount"
      , valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.claimAmount),
    },
    { headerName: 'Parent Claim ID', minWidth: 170, resizable: true,sortable: true, pinned: 'left', field: 'parent_patientctrlno', tooltipField: "parent_patientctrlno" },

    {
      headerName: 'Created On', minWidth: 150, sortable: true,resizable: true, filter: 'agDateColumnFilter',field: 'dateCreated', cellRenderer: params => {
        return DateFormatter(params.value)
      }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
        return SortDateValues(valueA, valueB)
      }, filterValueGetter: params => {
        return DateFormatter(params.data.dateCreated)
      }
    },
    { headerName: 'Created By', minWidth: 200, resizable: true, field: 'createdBy', filter: true,sortable: true, tooltipField: "createdBy" },
    { headerName: 'Action', minWidth: 115, resizable: true, lockVisible: true, cellRenderer: OpenClaimActionRender, sortable: false, type: 'centerAligned', filter: false, hide: false, pinned: 'right' },
  ]

  static ACCEPTED_COLUMN_DEFS(isClaimBillingMngmntGenrtEDI837File: boolean) {
    const ACCEPTED_COLUMN_DEFS: ColDef[] = [

      { headerName: '', width: 3, lockPosition: 'left', filter: false, resizable: false, field: '', pinned: 'left', cellRenderer: ChildClaimDeActiveClaimIndicatorRenderer,  },
      { headerName: '', minWidth: 2, lockPosition: 'left', filter: false, resizable: false, field: '', pinned: 'left', cellRenderer: AcceptedClaimLockerActionRenderer },
      { headerName: '', minWidth: 3, lockPosition: 'left', filter: false, resizable: false, field: '', pinned: 'left' ,
        checkboxSelection: params =>(params?.data?.claimId > 0 && ShowHideCheckboxClaimsGridAccept.showHideCheckbox(params, isClaimBillingMngmntGenrtEDI837File)),
        headerCheckboxSelection: isClaimBillingMngmntGenrtEDI837File,
        headerComponent: 'selectAllComponent'
      },
      { headerName: 'S.No', minWidth: 15, lockPosition: 'left', filter: false, resizable: false, field: '', pinned: 'left', valueGetter:  params => { return   params?.node?.id != null ? parseInt(params.node.id) + 1 : ''   },cellClass: 'ag-grid-row-number-cell' },
      { headerName: 'Claim ID', minWidth: 160, resizable: false, sortable: true, filter: true, pinned: 'left', field: 'claimControlNumber', tooltipField: 'claimControlNumber' },
      { headerName: 'Payer', minWidth: 200, resizable: false,sortable: true ,filter: true, field: 'payer', tooltipField: 'payer', valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params) },
      { headerName: 'Member', minWidth: 200, resizable: false,sortable: true ,filter: true, field: 'memberFullName', tooltipField: "memberFullName", valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params) },
      {
        headerName: 'DOS', minWidth: 200, resizable: false, field: 'dos',sortable: true, filter: 'agDateColumnFilter', tooltipField: "dos"
        , comparator(valueA, valueB, nodeA, nodeB) {
          return DOSSortingDashboard(nodeA.data.dosFrom, nodeB.data.dosFrom, nodeA.data.dosTo, nodeB.data.dosTo)
        }, filterValueGetter: params => {
          return DateFormatter(params.data.dos)
        }
      },
      { headerName: 'Rendering Provider', minWidth: 200, resizable: false,sortable: true, filter: true, field: 'renderingProviderFullName', tooltipField: "renderingProviderFullName", valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params) },
      {
        headerName: 'Claimed Amount', minWidth: 150, resizable: false, sortable: true,filter: true, cellClass: 'align-right', field: 'claimAmount', tooltipField: "claimAmount"
        , valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.claimAmount),
      },
      { headerName: 'Claim Type', minWidth: 150, resizable: false, sortable: true, filter: true, field: 'claimType', tooltipField: "claimType" },
      { headerName: 'Created By', minWidth: 200, resizable: false, field: 'createdBy', sortable: true, filter: true, tooltipField: "createdBy" },
      {
        headerName: 'Created On', minWidth: 150, resizable: true, sortable: true,filter: 'agDateColumnFilter',  field: 'dateCreated', cellRenderer: params => {
          return DateFormatter(params.value)
        }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
          return SortDateValues(valueA, valueB)
        }, filterValueGetter: params => {
          return DateFormatter(params?.data?.dateCreated)
        }
      },  
      {
        headerName: 'Age(DOC)', minWidth: 150, resizable: false, field: 'age',  sortable: true,filter: true
      },
      {
        headerName: 'Action', minWidth: 115, resizable: true, hide: false, lockVisible: false, cellRenderer: AcceptedClaimActionRenderer, sortable: false, type: 'centerAligned', filter: false, pinned: 'right',
      }

    ];
    return ACCEPTED_COLUMN_DEFS;
  }


  static DISPATCHED_COLUMN_DEFS(isClaimsBillingManagementResubmission: boolean) {

    const DISPATCHED_COLUMN_DEFS: ColDef[] = [
      { headerName: '', width: 3, lockPosition: 'left', filter: false, resizable: false, field: '', pinned: 'left', cellRenderer: ChildClaimDeActiveClaimIndicatorRenderer},
      { headerName: '', width: 50, lockPosition: 'left', filter: false, resizable: false, field: '', pinned: 'left', cellRenderer: DispatchedResubmsnExclaimarkRenderer},
      { headerName: '', minWidth: 5, lockPosition: 'left', filter: false, resizable: false, field: '', pinned: 'left' ,
         checkboxSelection: params => (params?.data?.resubmissionCount < COMMON_VALUES.maximumResubmissionCount && isClaimsBillingManagementResubmission) ? true : false,
        headerCheckboxSelection: isClaimsBillingManagementResubmission,headerComponent: 'selectAllComponent'
      },
      { headerName: 'S.No', minWidth: 15, lockPosition: 'left', filter: false, resizable: false, field: '', pinned: 'left', valueGetter:  params => { return   params?.node?.id != null ? parseInt(params.node.id) + 1 : ''   },cellClass: 'ag-grid-row-number-cell' },
      { headerName: 'Claim ID', minWidth: 160, sortable: true, filter: true, resizable: false, pinned: 'left', field: 'claimControlNumber', tooltipField: 'claimControlNumber' },
      { headerName: 'Payer', minWidth: 200,sortable: true, filter: true, resizable: false, field: 'payer', tooltipField: 'payer', valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params) },
      { headerName: 'Member', minWidth: 200, sortable: true, filter: true, resizable: false, field: 'memberFullName', tooltipField: "memberFullName", valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params) },
      { headerName: 'Rendering Provider', minWidth: 220, sortable: true, filter: true, resizable: false, field: 'renderingProviderFullName', tooltipField: "renderingProviderFullName" , valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params)},
      {
        headerName: 'DOS', minWidth: 200, resizable: false,sortable: true, filter: 'agDateColumnFilter', field: 'dos', tooltipField: "dos", comparator(valueA, valueB, nodeA, nodeB) {
          return DOSSortingDashboard(nodeA.data.dosFrom, nodeB.data.dosFrom, nodeA.data.dosTo, nodeB.data.dosTo)
        }, filterValueGetter: params => {return DateFormatter(params.data.dos)}
      },
      {
        headerName: 'Claimed Amount', minWidth: 150, resizable: false, sortable: true, filter: true,cellClass: 'align-right', field: 'claimAmount', tooltipField: "claimAmount"
        , valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.claimAmount),
      },
      { headerName: 'Sent To', minWidth: 200, resizable: false, field: 'sentTo',sortable: true, filter: true, tooltipField: "sentTo" },

      {
        headerName: 'Sent On', minWidth: 150, resizable: true, field: 'sentOn',sortable: true, filter: 'agDateColumnFilter', cellRenderer: params => {
          return DateFormatter(params.value)
        }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
          return SortDateValues(valueA, valueB)
        }, filterValueGetter: params => {
          return DateFormatter(params.data.sentOn)
        }
      },
      { headerName: 'Action', minWidth: 110, resizable: false, lockVisible: true, cellRenderer: DispatchedClaimActionRender, sortable: false, type: 'centerAligned', filter: false, hide: false, pinned: 'right' },

    ];
    return DISPATCHED_COLUMN_DEFS;
  }

  static UN_ACK_BY_CH_COLUMN_DEFS: ColDef[] = [
    { headerName: '', width: 3, lockPosition: 'left', filter: false, resizable: false, field: '', pinned: 'left', cellRenderer: ChildClaimDeActiveClaimIndicatorRenderer},
    { headerName: 'S.No', minWidth: 15, lockPosition: 'left', filter: false, resizable: false, field: '', pinned: 'left', valueGetter:  params => { return   params?.node?.id != null ? parseInt(params.node.id) + 1 : ''   },cellClass: 'ag-grid-row-number-cell' },
    { headerName: 'Claim ID', minWidth: 160, resizable: true, pinned: 'left', field: 'claimControlNumber',  sortable: true, filter: true,tooltipField: 'claimControlNumber' },
    { headerName: 'Payer', minWidth: 200, resizable: true, field: 'payer', tooltipField: 'payer', sortable: true, filter: true, valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params) },
    { headerName: 'Member', minWidth: 200, resizable: true, field: 'memberFullName', tooltipField: "memberFullName", sortable: true, filter: true, valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params) },
    { headerName: 'Rendering Provider', minWidth: 200, resizable: true, field: 'renderingProviderFullName', tooltipField: "renderingProviderFullName", sortable: true, filter: true, valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params) },
    {
      headerName: 'DOS', minWidth: 200, resizable: false, field: 'dos', tooltipField: "dos",sortable: true ,filter: 'agDateColumnFilter',  comparator(valueA, valueB, nodeA, nodeB) {
        return DOSSortingDashboard(nodeA.data.dosFrom, nodeB.data.dosFrom, nodeA.data.dosTo, nodeB.data.dosTo)
      }, filterValueGetter: params => {return DateFormatter(params.data.dos)}
    },
    {
      headerName: 'Claimed Amount', minWidth: 155, resizable: false, cellClass: 'align-right', field: 'claimAmount', tooltipField: "claimAmount",sortable: true ,filter: true,
      valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.claimAmount),
    },
    { headerName: 'Sent To', minWidth: 200, resizable: false, field: 'sentTo', tooltipField: "sentTo" ,sortable: true ,filter: true},

    {
      headerName: 'Sent On', minWidth: 150, resizable: false, field: 'sentOn',sortable: true ,filter: 'agDateColumnFilter', cellRenderer: params => {
        return DateFormatter(params.value)
      }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
        return SortDateValues(valueA, valueB)
      }, filterValueGetter: params => {
        return DateFormatter(params.data.sentOn)
      }
    },
    {
      headerName: 'Unack for (Days)', minWidth: 150, resizable: false, field: 'unackfor', tooltipField: "unackfor", sortable: true,filter: true
    },
    { headerName: 'Action', minWidth: 80, resizable: false, lockVisible: true, cellRenderer: UnAckByCHClaimActionRenderer, sortable: false, type: 'centerAligned', filter: false, hide: false, pinned: 'right' },

  ];

  static REJECTED_BY_CH_COLUMN_DEFS(isClaimsBillingManagementResubmission: boolean) {
    const REJECTED_BY_CH_COLUMN_DEFS: ColDef[] = [
      { headerName: '', minWidth: 5, filter: false, lockPosition: 'left', resizable: false, field: '', pinned: 'left', cellRenderer: ChildClaimDeActiveClaimIndicatorRenderer },
      { headerName: '', width: 50, lockPosition: 'left', filter: false, resizable: false, field: 'claimId', pinned: 'left', cellRenderer: ResubmissionExclaimarkRenderer },
      { headerName: '', minWidth: 5, lockPosition: 'left', filter: false, resizable: false, field: '', pinned: 'left',
        checkboxSelection: params => (!!params.data && params.data.resubmissionCount < COMMON_VALUES.maximumResubmissionCount && params.data.parent_patientctrlno == null && isClaimsBillingManagementResubmission) ? true : false,
        headerCheckboxSelection: isClaimsBillingManagementResubmission,headerComponent: 'selectAllComponent'
      },
      { headerName: 'S.No', minWidth: 15, lockPosition: 'left', filter: false, resizable: false, field: '', pinned: 'left', valueGetter:  params => { return   params?.node?.id != null ? parseInt(params.node.id) + 1 : ''   },cellClass: 'ag-grid-row-number-cell' },

      { headerName: 'Claim ID', minWidth: 160, resizable: true, sortable: true,filter: true, pinned: 'left', field: 'claimControlNumber', tooltipField: 'claimControlNumber' },
      { headerName: 'Payer', minWidth: 200, resizable: true,sortable: true,filter: true, field: 'payer', tooltipField: 'payer' , valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params) },
      { headerName: 'Member', minWidth: 200, resizable: true,sortable: true,filter: true, field: 'memberFullName', tooltipField: "memberFullName", valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params) },
      { headerName: 'Rendering Provider', minWidth: 200, resizable: true, sortable: true,filter: true,field: 'renderingProviderFullName', tooltipField: "renderingProviderFullName", valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params) },
      {
        headerName: 'DOS', minWidth: 200, resizable: true, field: 'dos', tooltipField: "dos", sortable: true, filter: 'agDateColumnFilter' ,comparator(valueA, valueB, nodeA, nodeB) {
          return DOSSortingDashboard(nodeA.data.dosFrom, nodeB.data.dosFrom, nodeA.data.dosTo, nodeB.data.dosTo)
        }, filterValueGetter: params => {return DateFormatter(params.data.dos)}
      },
      {
        headerName: 'Claimed Amount', minWidth: 150, resizable: false,sortable: true,filter: true, cellClass: 'align-right', field: 'claimAmount', tooltipField: "claimAmount"
        , valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.claimAmount),
      },
      {
        headerName: 'Created On', minWidth: 150, resizable: false,  sortable: true,filter: 'agDateColumnFilter',  field: 'dateCreated', cellRenderer: params => {
          return DateFormatter(params.value)
        }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
          return SortDateValues(valueA, valueB)
        }, filterValueGetter: params => {
          return DateFormatter(params.data.dateCreated)
        }
      },
      { headerName: 'Sent To', minWidth: 200, resizable: false, field: 'sentTo',sortable: true, filter: true,  tooltipField: "sentTo" },
      {
        headerName: 'Sent On', minWidth: 150, resizable: false,sortable: true,filter: 'agDateColumnFilter', field: 'sentOn', cellRenderer: params => {
          return DateFormatter(params.value)
        }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
          return SortDateValues(valueA, valueB)
        }, filterValueGetter: params => {
          return DateFormatter(params.data.sentOn)
        }
      },
      {
        headerName: 'Rejected On', minWidth: 150, resizable: false, field: '_999ProcessedOn_CH' , sortable: true, filter: 'agDateColumnFilter',
         valueFormatter: params =>  
        {
            return params?.data?.fileType == '005010X231A1' ? params?.data?._999ProcessedOn_CH  :params?.data?._277ProcessedOn_CH;
        }  
        , cellRenderer: params => {
          return DateFormatter(params.value)
        }
        ,
        tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
          return SortDateValues(valueA, valueB)
        }
        , filterValueGetter: params => {
          return DateFormatter(params.data.statusModifiedDate)
        }
      },
      {
        headerName: 'Rejected In', minWidth: 200, resizable: false, field: 'fileType', filter: true, tooltipField: "fileType", sortable: true,
        valueFormatter: params =>  
        {
            return params?.data?.fileType == '005010X231A1' ? '999' : (params?.data?.fileType == '005010X214' ?'277':null);
        }          

      },
      { headerName: 'Action', minWidth: 150, resizable: false, lockVisible: true, cellRenderer: RejectedByCHActionRenderer, sortable: false, type: 'centerAligned', filter: false, hide: false, pinned: 'right' },
    ];

    return REJECTED_BY_CH_COLUMN_DEFS;
  }

  static ACK_BY_CH_COLUMN_DEFS(isClaimsBillingManagementResubmission: boolean) {
    let ACK_BY_CH_COLUMN_DEFS: ColDef[] = [
      { headerName: '', width: 5, filter: false, lockPosition: 'left', resizable: true, field: '', pinned: 'left', cellRenderer: ChildClaimIndicatorRenderer },
      { headerName: '', width: 55, filter: false, lockPosition: 'left', resizable: true, field: '', pinned: 'left', cellRenderer: PendingResubmissionExclaimarkRenderer },

      { headerName: '', minWidth: 5, lockPosition: 'left', filter: false, resizable: true, field: '', pinned: 'left', 
        checkboxSelection: params => (!!params && params?.data?.resubmissionCount < COMMON_VALUES.maximumResubmissionCount && isClaimsBillingManagementResubmission) ? true : false,
        headerCheckboxSelection: isClaimsBillingManagementResubmission, headerComponent: 'selectAllComponent'
      },
      { headerName: 'S.No', minWidth: 15, lockPosition: 'left', filter: false, resizable: false, field: '', pinned: 'left', valueGetter:  params => { return   params?.node?.id != null ? parseInt(params.node.id) + 1 : ''   },cellClass: 'ag-grid-row-number-cell' },
      { headerName: 'Claim ID', minWidth: 160, resizable: false, pinned: 'left', sortable: true, filter: true,   field: 'claimControlNumber', tooltipField: 'claimControlNumber' },
      { headerName: 'Payer', minWidth: 200, resizable: false, field: 'payer', tooltipField: 'payer', sortable: true, filter: true,   valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params) },
      { headerName: 'Member', minWidth: 200, resizable: false, field: 'memberFullName', tooltipField: "memberFullName",sortable: true, filter: true,  valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params)  },
      { headerName: 'Rendering Provider', minWidth: 200, resizable: false, sortable: true, filter: true,  field: 'renderingProviderFullName', tooltipField: "renderingProviderFullName",valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params)  },
      {
        headerName: 'DOS', minWidth: 200, resizable: false, field: 'dos',sortable: true, filter: 'agDateColumnFilter',   tooltipField: "dos", comparator(valueA, valueB, nodeA, nodeB) {
          return DOSSortingDashboard(nodeA.data.dosFrom, nodeB.data.dosFrom, nodeA.data.dosTo, nodeB.data.dosTo)
        }, filterValueGetter: params => {return DateFormatter(params.data.dos)}
      },
      {
        headerName: 'Claimed Amount', minWidth: 150, resizable: false, sortable: true, filter: true,  cellClass: 'align-right', field: 'claimAmount', tooltipField: "claimAmount"
        , valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.claimAmount),
      },
      {
        headerName: 'Sent On', minWidth: 150, resizable: false, sortable: true, filter: 'agDateColumnFilter',   field: 'sentOn', cellRenderer: params => {
          return DateFormatter(params.value)
        }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
          return SortDateValues(valueA, valueB)
        }, filterValueGetter: params => {
          return DateFormatter(params.data.sentOn)
        }
      },
      {
        headerName: 'Accepted On', minWidth: 150, resizable: false, sortable: true, filter: 'agDateColumnFilter',   field: '_999ProcessedOn_CH', cellRenderer: params => {
          return DateFormatter(params.value)
        }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
          return SortDateValues(valueA, valueB)
        }, filterValueGetter: params => {
          return DateFormatter(params.data._999ProcessedOn_CH)
        }
      },
      { headerName: 'Action', minWidth: 110, resizable: false, lockVisible: true, cellRenderer: DispatchedClaimActionRender, sortable: false, type: 'centerAligned', filter: false, hide: false, pinned: 'right' },
    ];

    return ACK_BY_CH_COLUMN_DEFS;
  }

  static ACCEPTED_BY_CH_COLUMN_DEFS(isClaimsBillingManagementResubmission: boolean) {
    
    let ACCEPTED_BY_CH_COLUMN_DEFS: ColDef[] = [

      { headerName: '', width: 5, filter: false, lockPosition: 'left', resizable: true, field: '', pinned: 'left', cellRenderer: ChildClaimIndicatorRenderer },
      { headerName: '', width: 55, filter: false, lockPosition: 'left', resizable: true, field: '', pinned: 'left', cellRenderer: ResubmissionExclaimarkRenderer },
      { headerName: '', minWidth: 5, filter: false, lockPosition: 'left', resizable: false, field: '', pinned: 'left',  
        checkboxSelection: params => (params?.data?.resubmissionCount < COMMON_VALUES.maximumResubmissionCount && isClaimsBillingManagementResubmission) ? true : false,
        headerCheckboxSelection: isClaimsBillingManagementResubmission, headerComponent: 'selectAllComponent'

      },
      { headerName: 'S.No', minWidth: 15, lockPosition: 'left', filter: false, resizable: false, field: '', pinned: 'left', valueGetter:  params => { return   params?.node?.id != null ? parseInt(params.node.id) + 1 : ''   },cellClass: 'ag-grid-row-number-cell' },
      { headerName: 'Claim ID', minWidth: 160, resizable: false,sortable: true, filter: true, pinned: 'left', field: 'claimControlNumber', tooltipField: 'claimControlNumber' },
      { headerName: 'Payer', minWidth: 200, resizable: false,sortable: true, filter: true, field: 'payer', tooltipField: 'payer',valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params) },
      { headerName: 'Member', minWidth: 200, resizable: false, sortable: true, filter: true,field: 'memberFullName', tooltipField: "memberFullName",valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params) },
      { headerName: 'Rendering Provider', minWidth: 200, resizable: false, sortable: true, filter: true,field: 'renderingProviderFullName', tooltipField: "renderingProviderFullName",valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params) },
      {
        headerName: 'DOS', minWidth: 200, resizable: false, field: 'dos', sortable: true, filter: 'agDateColumnFilter',tooltipField: "dos", comparator(valueA, valueB, nodeA, nodeB) {
          return DOSSortingDashboard(nodeA.data.dosFrom, nodeB.data.dosFrom, nodeA.data.dosTo, nodeB.data.dosTo)
        }, filterValueGetter: params => {return DateFormatter(params.data.dos)}
      },
      {
        headerName: 'Claimed Amount', minWidth: 150, resizable: false, sortable: true, filter: true,cellClass: 'align-right', field: 'claimAmount', tooltipField: "claimAmount"
        , valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.claimAmount),
      },
      {
        headerName: 'Sent On', minWidth: 150, resizable: false, field: 'sentOn',sortable: true, filter: 'agDateColumnFilter', cellRenderer: params => {
          return DateFormatter(params.value)
        }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
          return SortDateValues(valueA, valueB)
        }, filterValueGetter: params => {
          return DateFormatter(params.data.sentOn)
        }
      },
      {
        headerName: 'Accepted On', minWidth: 150, resizable: false,sortable: true, filter: 'agDateColumnFilter', field: '_277ProcessedOn_CH', cellRenderer: params => {
          return DateFormatter(params.value)
        }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
          return SortDateValues(valueA, valueB)
        }, filterValueGetter: params => {
          return DateFormatter(params.data._277ProcessedOn_CH)
        }
      },
      { headerName: 'Action', minWidth: 110, resizable: false, lockVisible: true, cellRenderer: DispatchedClaimActionRender, sortable: false, type: 'centerAligned', filter: false, hide: false, pinned: 'right' },
    ];

    return ACCEPTED_BY_CH_COLUMN_DEFS;
  }

  static UN_ACK_BY_PAYER_COLUMN_DEFS: ColDef[] = [
    { headerName: '', minWidth: 5, filter: false, lockPosition: 'left', resizable: false, field: '', pinned: 'left', cellRenderer: ChildClaimDeActiveClaimIndicatorRenderer },
    { headerName: 'S.No', minWidth: 15, lockPosition: 'left', filter: false, resizable: false, field: '', pinned: 'left', valueGetter:  params => { return   params?.node?.id != null ? parseInt(params.node.id) + 1 : ''   },cellClass: 'ag-grid-row-number-cell' },
    { headerName: 'Claim ID', minWidth: 160, resizable: false, pinned: 'left',sortable: true, filter: true, field: 'claimControlNumber', tooltipField: 'claimControlNumber' },
    { headerName: 'Payer', minWidth: 200, resizable: false, sortable: true, filter: true,field: 'payer', tooltipField: 'payer',valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params) },
    { headerName: 'Member', minWidth: 200, resizable: false, sortable: true, filter: true,field: 'memberFullName', tooltipField: "memberFullName" ,valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params)},
    { headerName: 'Rendering Provider', minWidth: 200, resizable: false, sortable: true, filter: true, field: 'renderingProviderFullName', tooltipField: "renderingProviderFullName" ,valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params)},
    {
      headerName: 'DOS', minWidth: 200, resizable: false, field: 'dos', sortable: true, filter: 'agDateColumnFilter', tooltipField: "dos", comparator(valueA, valueB, nodeA, nodeB) {
        return DOSSortingDashboard(nodeA.data.dosFrom, nodeB.data.dosFrom, nodeA.data.dosTo, nodeB.data.dosTo)
      },
      filterValueGetter: params => {return DateFormatter(params.data.dos)}
    },
    {
      headerName: 'Claimed Amount', minWidth: 150, resizable: false,sortable: true, filter: true, cellClass: 'align-right', field: 'claimAmount', tooltipField: "claimAmount"
      , valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.claimAmount),
    },
    {
      headerName: 'Sent On', minWidth: 150, resizable: false, field: 'sentOn',sortable: true, filter: 'agDateColumnFilter', cellRenderer: params => {
        return DateFormatter(params.value)
      }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
        return SortDateValues(valueA, valueB)
      }, filterValueGetter: params => {
        return DateFormatter(params.data.sentOn)
      }
    },
    {
      headerName: 'Accepted On', minWidth: 150, resizable: false, sortable: true, filter: 'agDateColumnFilter',field: 'acceptedOn', cellRenderer: params => {
        return DateFormatter(params.value)
      }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
        return SortDateValues(valueA, valueB)
      }, filterValueGetter: params => {
        return DateFormatter(params.data.acceptedOn)
      }
    },
    { headerName: 'Action', minWidth: 110, resizable: false, lockVisible: true, cellRenderer: UnAckByPayerActionRenderer, sortable: false, type: 'centerAligned', filter: false, hide: false, pinned: 'right' },
  ];

  static ACK_BY_PAYER_COLUMN_DEFS(isClaimsBillingManagementResubmission: boolean) {
    const ACK_PAYER_COLUMN_DEFS: ColDef[] = [

      { headerName: '', minWidth: 5, filter: false, lockPosition: 'left', resizable: false, field: '', pinned: 'left', cellRenderer: ChildClaimDeActiveClaimIndicatorRenderer },
      { headerName: '', width: 50, lockPosition: 'left', filter: false, resizable: false, field: '', pinned: 'left', cellRenderer: PendingResubmissionExclaimarkRenderer },
      { headerName: '', minWidth: 5, filter: false, lockPosition: 'left', resizable: false, field: '', pinned: 'left',  
        checkboxSelection: params => (!!params.data && params.data.resubmissionCount < COMMON_VALUES.maximumResubmissionCount && params.data.parent_patientctrlno == null && isClaimsBillingManagementResubmission) ? true : false,
        headerCheckboxSelection: isClaimsBillingManagementResubmission, headerComponent: 'selectAllComponent'
      },
      { headerName: 'S.No', minWidth: 5, filter: false, lockPosition: 'left', resizable: false, field: '', pinned: 'left',   valueGetter:  params => { return   params?.node?.id != null ? parseInt(params.node.id) + 1 : ''   },cellClass: 'ag-grid-row-number-cell'},
      { headerName: 'Claim ID', minWidth: 160, resizable: true, pinned: 'left',sortable: true, filter: true, field: 'claimControlNumber', tooltipField: 'claimControlNumber' },
      { headerName: 'Payer', minWidth: 200, resizable: true, field: 'payer', sortable: true, filter: true, tooltipField: 'payer' ,valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params)  },
      { headerName: 'Member', minWidth: 200, resizable: true, field: 'memberFullName', sortable: true, filter: true, tooltipField: "memberFullName" ,valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params)  },
      { headerName: 'Rendering Provider', minWidth: 200, resizable: true, field: 'renderingProviderFullName', sortable: true, filter: true, tooltipField: "renderingProviderFullName",valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params)  },
      {
        headerName: 'DOS', minWidth: 200, resizable: true, field: 'dos', sortable: true, filter: 'agDateColumnFilter', tooltipField: "dos", comparator(valueA, valueB, nodeA, nodeB) {
          return DOSSortingDashboard(nodeA.data.dosFrom, nodeB.data.dosFrom, nodeA.data.dosTo, nodeB.data.dosTo)
        }, filterValueGetter: params => {return DateFormatter(params.data.dos)}
      },
      {
        headerName: 'Claimed Amount', minWidth: 150, resizable: true, cellClass: 'align-right', sortable: true, filter: true,  field: 'claimAmount', tooltipField: "claimAmount"
        , valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.claimAmount),
      },
      {
        headerName: 'Sent On', minWidth: 150, resizable: true, field: 'sentOn', sortable: true, filter: 'agDateColumnFilter',  cellRenderer: params => {
          return DateFormatter(params.value)
        }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
          return SortDateValues(valueA, valueB)
        }, filterValueGetter: params => {
          return DateFormatter(params.data.sentOn)
        }
      },
      {
        headerName: 'Accepted On', minWidth: 150, resizable: true, field: 'acceptedOn', sortable: true, filter: 'agDateColumnFilter',  cellRenderer: params => {
          return DateFormatter(params.value)
        }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
          return SortDateValues(valueA, valueB)
        }, filterValueGetter: params => {
          return DateFormatter(params.data.acceptedOn)
        }
      },
      { headerName: 'Action', minWidth: 110, resizable: true, lockVisible: true, cellRenderer: DispatchedClaimActionRender, sortable: false, type: 'centerAligned', filter: false, hide: false, pinned: 'right' },
    ];

    return ACK_PAYER_COLUMN_DEFS;
  }

  static ACCEPTED_BY_PAYER(isClaimsBillingManagementResubmission: boolean) {
    let ACCEPTED_BY_PAYER: ColDef[] = [
      { headerName: '', minWidth: 5, filter: false, lockPosition: 'left', resizable: false, field: '', pinned: 'left', cellRenderer: ChildClaimDeActiveClaimIndicatorRenderer },
      { headerName: '', width: 50, lockPosition: 'left', filter: false, resizable: false, field: '', pinned: 'left', cellRenderer: DispatchedResubmsnExclaimarkRenderer},
      { headerName: '', minWidth: 5, filter: false, lockPosition: 'left', resizable: false, field: '', pinned: 'left' ,
         checkboxSelection: params => (params?.data?.resubmissionCount < COMMON_VALUES.maximumResubmissionCount && isClaimsBillingManagementResubmission) ? true : false,
        headerCheckboxSelection: isClaimsBillingManagementResubmission,headerComponent: 'selectAllComponent'
      },
      { headerName: 'S.No', minWidth: 5, filter: false, lockPosition: 'left', resizable: false, field: '', pinned: 'left',   valueGetter:  params => { return   params?.node?.id != null ? parseInt(params.node.id) + 1 : ''   },cellClass: 'ag-grid-row-number-cell'},
      { headerName: 'Claim ID', minWidth: 160, resizable: false, sortable: true, filter: true,  pinned: 'left', field: 'claimControlNumber', tooltipField: 'claimControlNumber' },
      { headerName: 'Payer', minWidth: 200, resizable: false, sortable: true, filter: true,  field: 'payer', tooltipField: 'payer', valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params) },
      { headerName: 'Member', minWidth: 200, resizable: true,  sortable: true, filter: true, field: 'memberFullName', tooltipField: "memberFullName",valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params) },
      { headerName: 'Rendering Provider', minWidth: 200, resizable: true, sortable: true, filter: true, field: 'renderingProviderFullName', tooltipField: "renderingProviderFullName",valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params) },
      {
        headerName: 'DOS', minWidth: 200, resizable: false,  filter: 'agDateColumnFilter' ,sortable: true,field: 'dos', tooltipField: "dos", comparator(valueA, valueB, nodeA, nodeB) {
          return DOSSortingDashboard(nodeA.data.dosFrom, nodeB.data.dosFrom, nodeA.data.dosTo, nodeB.data.dosTo)
        }, filterValueGetter: params => {return DateFormatter(params.data.dos)}
      },
      {
        headerName: 'Claimed Amount', minWidth: 150, resizable: true, cellClass: 'align-right',  sortable: true, filter: true ,field: 'claimAmount', tooltipField: "claimAmount"
        , valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.claimAmount),
      },
      {
        headerName: 'Sent On', minWidth: 150, resizable: true, field: 'sentOn', filter: 'agDateColumnFilter' ,sortable: true, cellRenderer: params => {
          return DateFormatter(params.value)
        }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
          return SortDateValues(valueA, valueB)
        }, filterValueGetter: params => {
          return DateFormatter(params.data.sentOn)
        }
      },
      {
        headerName: 'Accepted On', minWidth: 150, resizable: true, field: '_277ProcessedOn_Payer',filter: 'agDateColumnFilter' ,sortable: true,  cellRenderer: params => {
          return DateFormatter(params.value)
        }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
          return SortDateValues(valueA, valueB)
        }, filterValueGetter: params => {
          return DateFormatter(params.data._277ProcessedOn_Payer)
        }
      },

      { headerName: 'Action', minWidth: 110, resizable: true, lockVisible: true, cellRenderer: DispatchedClaimActionRender, sortable: false, type: 'centerAligned', filter: false, hide: false, pinned: 'right' },
    ];
    return ACCEPTED_BY_PAYER;
  }

  static REJECTED_BY_PAYER_COLUMN_DEFS(isClaimsBillingManagementResubmission: boolean) {
    const REJECTED_BY_PAYER_COLUMN_DEFS: ColDef[] = [
      { headerName: '', minWidth: 5, filter: false, lockPosition: 'left', resizable: false, field: '', pinned: 'left', cellRenderer: ChildClaimDeActiveClaimIndicatorRenderer },
      { headerName: '', minWidth: 50, filter: false, lockPosition: 'left', resizable: false, field: '', pinned: 'left', cellRenderer: PendingResubmissionExclaimarkRenderer },
      { headerName: '', minWidth: 10, filter: false, lockPosition: 'left', resizable: false, field: '', pinned: 'left',
         checkboxSelection: params => (!!params?.data && params.data.resubmissionCount < COMMON_VALUES.maximumResubmissionCount && isClaimsBillingManagementResubmission) ? true : false,
        headerCheckboxSelection: isClaimsBillingManagementResubmission, headerComponent: 'selectAllComponent'
      },
      { headerName: 'S.No', minWidth: 5, filter: false, lockPosition: 'left', resizable: false, field: '', pinned: 'left',   valueGetter:  params => { return   params?.node?.id != null ? parseInt(params.node.id) + 1 : ''   },cellClass: 'ag-grid-row-number-cell'},
      { headerName: 'Claim ID', minWidth: 160, resizable: false, pinned: 'left',sortable: true, filter: true, field: 'claimControlNumber', tooltipField: 'claimControlNumber' },
      { headerName: 'Payer', minWidth: 200, resizable: false, field: 'payer', tooltipField: 'payer', sortable: true, filter: true,valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params) },
      { headerName: 'Member', minWidth: 200, resizable: false, field: 'memberFullName', tooltipField: "memberFullName", sortable: true, filter: true ,valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params) },
      { headerName: 'Rendering Provider', minWidth: 200, resizable: false, field: 'renderingProviderFullName', tooltipField: "renderingProviderFullName", sortable: true, filter: true,valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params)  },
      {
        headerName: 'DOS', minWidth: 200, resizable: false, field: 'dos', tooltipField: "dos",sortable: true, filter: 'agDateColumnFilter', comparator(valueA, valueB, nodeA, nodeB) {
          return DOSSortingDashboard(nodeA.data.dosFrom, nodeB.data.dosFrom, nodeA.data.dosTo, nodeB.data.dosTo)
        }, filterValueGetter: params => {return DateFormatter(params.data.dos)}
      },
      {
        headerName: 'Claimed Amount', minWidth: 150, resizable: false, cellClass: 'align-right', field: 'claimAmount', sortable: true, filter: true ,tooltipField: "claimAmount"
        , valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.claimAmount),
      },
      {
        headerName: 'Sent On', minWidth: 150, resizable: false, field: 'sentOn',sortable: true, filter: 'agDateColumnFilter', cellRenderer: params => {
          return DateFormatter(params.value)
        }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
          return SortDateValues(valueA, valueB)
        }, filterValueGetter: params => {
          return DateFormatter(params.data.sentOn)
        }
      },
      {
        headerName: 'Rejected On', minWidth: 150, resizable: false, field: '_277ProcessedOn_Payer',sortable: true, filter: 'agDateColumnFilter', cellRenderer: params => {
          return DateFormatter(params.value)
        }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
          return SortDateValues(valueA, valueB)
        }, filterValueGetter: params => {
          return DateFormatter(params.data._277ProcessedOn_Payer)
        }
      },
      { headerName: 'Action', minWidth: 150, resizable: false, lockVisible: true, cellRenderer: RejectedByCHActionRenderer, sortable: false, type: 'centerAligned', filter: false, hide: false, pinned: 'right' },
    ];
    return REJECTED_BY_PAYER_COLUMN_DEFS;
  }

  static PENDING_COLUMN_DEFS(isClaimsBillingManagementResubmission: boolean) {  
    const PENDING: ColDef[] = [
      { headerName: '', minWidth: 5, filter: false, lockPosition: 'left', resizable: false, field: '', pinned: 'left', cellRenderer: ChildClaimDeActiveClaimIndicatorRenderer },
      { headerName: '', minWidth: 50, filter: false, lockPosition: 'left', resizable: false, field: '', pinned: 'left', cellRenderer: PendingResubmissionExclaimarkRenderer },
      { headerName: '', minWidth: 50, filter: false, lockPosition: 'left', resizable: false, field: '', pinned: 'left', 
        checkboxSelection: params => (!!params.data && params.data.resubmissionCount < COMMON_VALUES.maximumResubmissionCount && isClaimsBillingManagementResubmission) ? true : false,
        headerCheckboxSelection: isClaimsBillingManagementResubmission  , headerComponent: 'selectAllComponent'
      },
      { headerName: 'S.No', minWidth: 5, filter: false, lockPosition: 'left', resizable: false, field: '', pinned: 'left',   valueGetter:  params => { return   params?.node?.id != null ? parseInt(params.node.id) + 1 : ''   },cellClass: 'ag-grid-row-number-cell'},

      { headerName: 'Claim ID', minWidth: 160, resizable: false, pinned: 'left', sortable: true, filter: true ,field: 'claimControlNumber', tooltipField: 'claimControlNumber' },
      { headerName: 'Payer', minWidth: 200, resizable: false, field: 'payer', tooltipField: 'payer', sortable: true, filter: true,valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params) },
      { headerName: 'Member', minWidth: 200, resizable: false, field: 'memberFullName', tooltipField: "memberFullName", sortable: true, filter: true ,valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params)},
      { headerName: 'Rendering Provider', minWidth: 200, resizable: false, field: 'renderingProviderFullName', tooltipField: "renderingProviderFullName", sortable: true, filter: true,valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params)},
      {
        headerName: 'DOS', minWidth: 200, resizable: false, field: 'dos', tooltipField: "dos",  sortable: true, filter: 'agDateColumnFilter', comparator(valueA, valueB, nodeA, nodeB) {
          return DOSSortingDashboard(nodeA.data.dosFrom, nodeB.data.dosFrom, nodeA.data.dosTo, nodeB.data.dosTo)
        }, filterValueGetter: params => {return DateFormatter(params.data.dos)}
      },

      {
        headerName: 'Claimed Amount', minWidth: 150, resizable: true, cellClass: 'align-right', sortable: true, filter: true, field: 'claimAmount', tooltipField: "claimAmount"
        , valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.claimAmount),
      },
      {
        headerName: 'Created On', minWidth: 150, resizable: true, field: 'dateCreated', sortable: true, filter: 'agDateColumnFilter', cellRenderer: params => {
          return DateFormatter(params.value)
        }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
          return SortDateValues(valueA, valueB)
        }, filterValueGetter: params => {
          return DateFormatter(params.data.dateCreated)
        }
      },
      { headerName: 'Sent To', minWidth: 200, resizable: true, field: 'sentTo', tooltipField: "sentTo", sortable: true, filter: true ,valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params)},
      {
        headerName: 'Sent On', minWidth: 150, resizable: true, field: 'sentOn', sortable: true, filter: 'agDateColumnFilter', cellRenderer: params => {
          return DateFormatter(params.value)
        }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
          return SortDateValues(valueA, valueB)
        }, filterValueGetter: params => {
          return DateFormatter(params.data.sentOn)
        }
      },
      {
        headerName: 'Pend On', minWidth: 150, resizable: true, field: '_277PendOnDate',  sortable: true, filter: 'agDateColumnFilter', cellRenderer: params => {
          return DateFormatter(params.value)
        }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
          return SortDateValues(valueA, valueB)
        }, filterValueGetter: params => {
          return DateFormatter(params.data._277PendOnDate)
        }
      },

      { headerName: 'Action', minWidth: 110, resizable: false, lockVisible: true, cellRenderer: DispatchedClaimActionRender, sortable: false, type: 'centerAligned', filter: false, hide: false, pinned: 'right' },
    ];
    return PENDING;
  }

  static EOB_RECIEVED(isClaimsBillingManagementResubmission: boolean) {
    const EOB_RECIEVED: ColDef[] = [

      { headerName: '', minWidth: 5, filter: false, lockPosition: 'left', resizable: false, field: '', pinned: 'left', cellRenderer: ChildClaimDeActiveClaimIndicatorRenderer },
      { headerName: '', minWidth: 50, filter: false, lockPosition: 'left', resizable: false, field: '', pinned: 'left', cellRenderer: PendingResubmissionExclaimarkRenderer },
      { headerName: '', minWidth: 15, filter: false, lockPosition: 'left', resizable: false, field: '', pinned: 'left', 
        checkboxSelection: params => (!!params?.data && params.data.resubmissionCount < COMMON_VALUES.maximumResubmissionCount && isClaimsBillingManagementResubmission) ? true : false,
        headerCheckboxSelection: isClaimsBillingManagementResubmission, headerComponent: 'selectAllComponent'
       },
      { headerName: 'S.No', minWidth: 5, filter: false, lockPosition: 'left', resizable: false, field: '', pinned: 'left',   valueGetter:  params => { return   params?.node?.id != null ? parseInt(params.node.id) + 1 : ''   },cellClass: 'ag-grid-row-number-cell'},

      { headerName: 'Claim ID', minWidth: 160, resizable: false, sortable: true, filter: true, pinned: 'left', field: 'claimControlNumber', tooltipField: 'claimControlNumber' },
      { headerName: 'Payer', minWidth: 200, resizable: false,sortable: true, filter: true,  field: 'payer', tooltipField: 'payer',valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params) },
      { headerName: 'Member', minWidth: 200, resizable: false, sortable: true, filter: true, field: 'memberFullName', tooltipField: "memberFullName",valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params) },
      { headerName: 'Rendering Provider', minWidth: 200, resizable: false, sortable: true, filter: true,  field: 'renderingProviderFullName', tooltipField: "renderingProviderFullName" ,valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params)},
      {
        headerName: 'DOS', minWidth: 200, resizable: false, field: 'dos',sortable: true, filter: 'agDateColumnFilter',tooltipField: "dos", comparator(valueA, valueB, nodeA, nodeB) {
          return DOSSortingDashboard(nodeA.data.dosFrom, nodeB.data.dosFrom, nodeA.data.dosTo, nodeB.data.dosTo)
        }, filterValueGetter: params => {return DateFormatter(params.data.dos)}
      },
      {
        headerName: 'Claimed Amount', minWidth: 150, resizable: false, sortable: true, filter: true,  cellClass: 'align-right', field: 'claimAmount', tooltipField: "claimAmount"
        , valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.claimAmount),
      },
      {
        headerName: 'Sent On', minWidth: 150, resizable: false, field: 'sentOn', sortable: true, filter: 'agDateColumnFilter', cellRenderer: params => {
          return DateFormatter(params.value)
        }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
          return SortDateValues(valueA, valueB)
        }, filterValueGetter: params => {
          return DateFormatter(params.data.sentOn)
        }
      },
      {
        headerName: 'Paid Amount', minWidth: 150, resizable: false, sortable: true, filter: true,  cellClass: 'align-right', field: 'paidAmount', tooltipField: "paidAmount"
        , valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.paidAmount),
      },
      {
        headerName: 'Paid On', minWidth: 150, resizable: false, field: '_835PaidOnDate',sortable: true, filter: 'agDateColumnFilter', cellRenderer: params => {
          return DateFormatter(params.value)
        }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
          return SortDateValues(valueA, valueB)
        }, filterValueGetter: params => {
          return DateFormatter(params.data._835PaidOnDate)
        }
      },
      { headerName: 'Action', minWidth: 145, resizable: true, lockVisible: true, cellRenderer: EobRecievedClaimActionRenderer, sortable: false, type: 'centerAligned', filter: false, hide: false, pinned: 'right' },
    ]
    return EOB_RECIEVED;
  }

  static DENIED_BY_PAYER(isClaimsBillingManagementResubmission: boolean) {
    const DENIED_BY_PAYER: ColDef[] = [

      { headerName: '', minWidth: 5, filter: false, lockPosition: 'left', resizable: false, field: '', pinned: 'left', cellRenderer: ChildClaimDeActiveClaimIndicatorRenderer },
      { headerName: '', minWidth: 50, filter: false, lockPosition: 'left', resizable: false, field: '', pinned: 'left', cellRenderer: PendingResubmissionExclaimarkRenderer },
      { headerName: '', minWidth: 15, filter: false, lockPosition: 'left', resizable: false, field: '', pinned: 'left', 
         checkboxSelection: params => (!!params.data && params.data.resubmissionCount < COMMON_VALUES.maximumResubmissionCount && isClaimsBillingManagementResubmission) ? true : false,
        headerCheckboxSelection: isClaimsBillingManagementResubmission, headerComponent: 'selectAllComponent'
      },
      { headerName: 'S.No', minWidth: 5, filter: false, lockPosition: 'left', resizable: false, field: '', pinned: 'left',   valueGetter:  params => { return   params?.node?.id != null ? parseInt(params.node.id) + 1 : ''   },cellClass: 'ag-grid-row-number-cell'},
      { headerName: 'Claim ID', minWidth: 160, resizable: true,sortable: true, filter: true, pinned: 'left', field: 'claimControlNumber', tooltipField: 'claimControlNumber' },
      { headerName: 'Payer', minWidth: 200, resizable: true,sortable: true, filter: true, field: 'payer', tooltipField: 'payer',valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params) },
      { headerName: 'Member', minWidth: 200, resizable: true, sortable: true, filter: true,field: 'memberFullName', tooltipField: "memberFullName" ,valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params)},
      { headerName: 'Rendering Provider', minWidth: 200, resizable: true,sortable: true, filter: true, field: 'renderingProviderFullName', tooltipField: "renderingProviderFullName" ,valueFormatter: (params: ValueFormatterParams) => COMMON_METHODS.truncateTextFormatter(params)},
      {
        headerName: 'DOS', minWidth: 200, resizable: true, field: 'dos', sortable: true, filter: 'agDateColumnFilter',tooltipField: "dos", comparator(valueA, valueB, nodeA, nodeB) {
          return DOSSortingDashboard(nodeA.data.dosFrom, nodeB.data.dosFrom, nodeA.data.dosTo, nodeB.data.dosTo)
        }, filterValueGetter: params => {return DateFormatter(params.data.dos)}
      },
      {
        headerName: 'Claimed Amount', minWidth: 150, resizable: true,sortable: true, filter: true, cellClass: 'align-right', field: 'claimAmount', tooltipField: "claimAmount"
        , valueFormatter: params => COMMON_METHODS.currencyFormatter(params?.data?.claimAmount),
      },
      {
        headerName: 'Created On', minWidth: 150, resizable: true,sortable: true, filter: 'agDateColumnFilter', field: 'dateCreated', cellRenderer: params => {
          return DateFormatter(params.value)
        }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
          return SortDateValues(valueA, valueB)
        }, filterValueGetter: params => {
          return DateFormatter(params.data.dateCreated)
        }
      },
      {
        headerName: 'Sent On', minWidth: 150, resizable: true, sortable: true, filter: 'agDateColumnFilter', field: 'sentOn', cellRenderer: params => {
          return DateFormatter(params.value)
        }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
          return SortDateValues(valueA, valueB)
        }, filterValueGetter: params => {
          return DateFormatter(params.data.sentOn)
        }
      },

      {
        headerName: 'Denied On', minWidth: 150, resizable: true,sortable: true, filter: 'agDateColumnFilter',  field: '_835PaidOnDate', cellRenderer: params => {
          return DateFormatter(params.value)
        }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
          return SortDateValues(valueA, valueB)
        }, filterValueGetter: params => {
          return DateFormatter(params.data._835PaidOnDate)
        }
      },
      { headerName: 'Action', minWidth: 145, resizable: true, lockVisible: true, cellRenderer: EobRecievedClaimActionRenderer, sortable: false, type: 'centerAligned', filter: false, hide: false, pinned: 'right' },
    ];
    return DENIED_BY_PAYER;
  }

}


export class EOB_REPORT_GRID_COLUMNS {

  static EOB_REPORT_COLDEF: ColDef[] = [
    { headerName: 'Primary Payer', minWidth: 180, resizable: true, filter: false, field: 'payerName', },
    { headerName: 'Secondary Payer', minWidth: 180, resizable: true, filter: false, field: 'secondaryPayerName', },
    { headerName: 'Patient Name', minWidth: 190, resizable: true, filter: false, field: 'patientName', },
    {
      headerName: 'DOS', minWidth: 150, resizable: true, field: 'dateOfService', filter: false, cellRenderer: params => {
        return DateFormatter(params.value)
      }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
        return SortDateValues(valueA, valueB)
      }, filterValueGetter: params => {
        return DateFormatter(params.data.dateCreated)
      }
    },

    { headerName: 'CPT', minWidth: 100, resizable: true, filter: false, field: 'cpt', },
    {
      headerName: 'Billed', minWidth: 100, resizable: true, cellClass: 'align-right', filter: false, field: 'billed',
      valueFormatter: params => COMMON_METHODS.currencyFormatter(params.data.billed),
    },
    {
      headerName: 'Allowed', minWidth: 80, cellClass: 'align-right', resizable: true, filter: false, field: 'allowed',
      valueFormatter: params => COMMON_METHODS.currencyFormatter(params.data.allowed),
    },
    {
      headerName: 'ADJ', minWidth: 80, cellClass: 'align-right', resizable: true, filter: false, tooltipField: 'adj', field: "adj", cellRenderer: EobReportAnchorRenderer,
    },
    {
      headerName: 'DED', cellClass: 'align-right', minWidth: 80, resizable: true, filter: false, field: 'ded',
      valueFormatter: params => COMMON_METHODS.currencyFormatter(params.data.ded),
    },
    {
      headerName: 'COINS/PR', minWidth: 110, cellClass: 'align-right', resizable: true, filter: false, field: 'coIns', tooltipField: "coIns", cellRenderer: EobReportAnchorRenderer,
      valueFormatter: params => COMMON_METHODS.currencyFormatter(params.data.coIns),
    },
    { headerName: 'PMT/AMT', cellClass: 'align-right', minWidth: 110, resizable: true, filter: false, field: 'paymentAmount', valueFormatter: params => COMMON_METHODS.currencyFormatter(params.data.paymentAmount) },
    { headerName: 'Penalty', minWidth: 110, cellClass: 'align-right', resizable: true, filter: false, field: 'penalty', tooltipField: "penalty", cellRenderer: EobReportAnchorRenderer, valueFormatter: params => COMMON_METHODS.currencyFormatter(params.data.penalty), },
    {
      headerName: 'Paid', minWidth: 80, cellClass: 'align-right', resizable: true, filter: false, field: 'paid',
      valueFormatter: params => COMMON_METHODS.currencyFormatter(params.data.paid),
    },
    { headerName: 'Reason', minWidth: 200, resizable: true, filter: false, cellRenderer: EobReportsReasonRenderer },
  ]
}


export class CLAIM_RESUBMIT_GRID_COLUMNS {
  static CLAIM_RESUBMIT_COL_DEF: ColDef[] = [
    { headerName: '', width: 10, filter: false, lockPosition: 'left', resizable: true, field: 'claimControlNumber', pinned: 'left', cellRenderer: InActiveClaimStatusColorIndicatorRenderer },
    { headerName: 'Claim ID', minWidth: 160, resizable: true, pinned: 'left', field: 'patientControlNumber', tooltipField: 'patientControlNumber' },
    // { headerName: 'DOS', minWidth: 110, resizable: true,   field: 'dos', tooltipField: "dos", cellRenderer: params => {
    //   return DateFormatter(params.value)
    // }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
    //   return SortDateValues(valueA, valueB)
    // }, filterValueGetter: params => {
    //   return DateFormatter(params.data.dosFrom)
    // } },

    { headerName: 'DOS', minWidth: 200, resizable: true, field: 'dos', tooltipField: "dos" },
    { headerName: 'Member', minWidth: 150, resizable: true, field: 'memberName', tooltipField: "memberName" },
    { headerName: 'Rendering Provider', minWidth: 200, resizable: true, field: 'providerName', tooltipField: "providerName" },
    { headerName: 'Created By', minWidth: 200, resizable: true, field: 'createdBy', filter: true, tooltipField: "createdBy" },
    {
      headerName: 'Create Date', minWidth: 150, resizable: true, field: 'createdDate', tooltipField: "createdDate", cellRenderer: params => {
        return DateFormatter(params.value)
      }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
        return SortDateValues(valueA, valueB)
      }, filterValueGetter: params => {
        return DateFormatter(params.data.dateCreated)
      },
    },
    { headerName: 'Current Status', minWidth: 150, resizable: true, field: 'status', tooltipField: "status" },

  ]
}

export class ON_HOLD_CATEGORY_DESCRIPTION_COLUMNS {
  static CATEGORY_DESCRIPTION_COL_DEF: ColDef[] = [
    { headerName: 'Category', minWidth: 250, resizable: true, filter: false, field: 'reason', tooltipField: "reason" },
    { headerName: 'Description', minWidth: 280, resizable: true, filter: false, field: 'description', tooltipField: "description" },
    {

      headerName: 'Modified Date', minWidth: 200, resizable: true, filter: false, field: 'lastModifiedDate', cellRenderer: params => {
        return DateFormatterWithTime(params.value)
      }, tooltipValueGetter: params => { return DateFormatterWithTime(params.value) }, comparator(valueA, valueB) {
        return SortDateValues(valueA, valueB)
      }, filterValueGetter: params => {
        return DateFormatterWithTime(params.data.dateCreated)
      },
    }
  ]
}


export class ShowHideCheckboxClaimsGrid {

  static showHideCheckbox(paramsDetails: any) {
    let params = paramsDetails.data;
    // if (params.resubmissionCount >= 50) {
    //   return false;
    // }
    if (params.claimStatusCode == CLAIM_TYPE.ntr) {
      return true;
    }
    if (params.claimStatusCode == CLAIM_TYPE.unackByCH || params.claimStatusCode == CLAIM_TYPE.unAckByPayer) {
      return false;
    }
    else if (params.claimStatusCode == CLAIM_TYPE.accepted) {
      if (params.isLocked) {
        return false;
      }
      if (params.clearingHouseId == null || params.clearingHouseId == "") {
        return false;
      }
    }
    else if (params.claimStatusCode == CLAIM_TYPE.open) {
      if (params.claimId > 0 && params.isActive && params.source && params.source.toUpperCase() == COMMON_VALUES.GATEWAY) {
        return true;
      }
      else {
        return false;
      }
    }
    else if (params.claimStatusCode == CLAIM_TYPE.dispatched || params.claimStatusCode == CLAIM_TYPE.acceptedByPayer || params.claimStatusCode == CLAIM_TYPE.pending || params.claimStatusCode == CLAIM_TYPE.eobReceived
      || params.claimStatusCode == CLAIM_TYPE.rejectedByPayer) {
      if (params.resubmissionCount < COMMON_VALUES.maximumResubmissionCount) {
        return true;
      } else {
        return false;
      }
    }
    else if (params.claimStatusCode == CLAIM_TYPE.rejectedByCH || params.claimStatusCode == CLAIM_TYPE.acknolwedgedByPayer) {
      if (params.resubmissionCount < COMMON_VALUES.maximumResubmissionCount && params.parent_patientctrlno == null) {
        return true;
      } else {
        return false;
      }
    }
    // else 
    // {
    //   console.log('open isActive else',params)
    //   if(params.parent_patientctrlno!=null)
    //   {
    //     return false;
    //   }
    //   return true;
    // }
    return true;
  }


}

export class ShowHideCheckboxClaimsGridAccept {
  static showHideCheckbox(paramsDetails: any, isClaimBillingMngmntGenrtEDI837File: boolean) {
    let params = paramsDetails?.data; 
    if (params?.clearingHouseId == null || params?.isLocked == true && isClaimBillingMngmntGenrtEDI837File)
      return false;
    else
      return isClaimBillingMngmntGenrtEDI837File;
  }
}


export class GridCustomizeTableConstants {
  public static Global_Search_GridCustomizeTable: Customize[] = [
      { name: '', field: 'claimControlNumber', selected: true, disabled: false },
      { name: 'Claim Id', field: 'claimId', selected: true, disabled: false },
      { name: 'Member First Name', field: 'displayMemberFirstName', selected: true, disabled: false },
      { name: 'Member Last Name', field: 'displayMemberLastName', selected: true, disabled: false },
      { name: 'Member ID', field: 'subscribeId', selected: true, disabled: false },
      { name: 'IPA', field: 'ipaName', selected: true, disabled: false },
      { name: 'Federal Tax ID', field: 'taxId', selected: true, disabled: false },
      { name: 'Date Of Birth',  field: 'patientDOB', selected:true, disabled : false },
      { name: 'DOS From', field: 'dosFrom', selected: true, disabled: false },
      { name: 'DOS To', field: 'dosTo', selected: true, disabled: false },
      { name: 'DOC', field: 'doc', selected: true, disabled: false },
      { name: 'Billing Provider First Name', field: 'billingProviderFirstName', selected: true, disabled: false },
      { name: 'Billing Provider Last Name', field: 'billingProviderLastName', selected: true, disabled: false },
      { name: 'Billing Provider NPI', field: 'billingProviderNpi', selected: true, disabled: false },
      { name: 'Rendering Provider First Name', field: 'renderingProviderFirstName', selected: true, disabled: false },
      { name: 'Rendering Provider Last Name', field: 'renderingProviderLastName', selected: true, disabled: false },
      { name: 'Rendering Provider NPI', field: 'renderingProviderNPI', selected: true, disabled: false },
      { name: 'Age', field: 'age', selected: true, disabled: false },
      { name: 'CPT Code(s)', field: 'cptCodes', selected: true, disabled: false },
      { name: 'ICD Code(s)', field: 'icdCodes', selected: true, disabled: false },
      { name: 'Insurance Name', field: 'insuranceName', selected: true, disabled: false },
      { name: 'Bucket Name', field: 'bucketName', selected: true, disabled: false },
      { name: 'POS', field: 'placeOfService', selected: true, disabled: false },
      { name: 'Biller Name', field: 'billerName', selected: true, disabled: false },
      { name: 'Source', field: 'source', selected: true, disabled: false },
      { name: 'MBI', field: 'mbi', selected: true, disabled: false },
      { name: 'Unique Encounter Number', field: 'uniqueEncounterNumber', selected: true, disabled: false },
      { name: 'Resubmission Count', field: 'resubmissionCount', selected: true, disabled: false }
  ];


}
